<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技馆伴游助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 紧凑的基本信息区域 */
        .basic-info {
            padding: 15px;
            background: white;
            border-bottom: 1px solid #e0e0e0;
        }

        .info-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .museum-thumb {
            width: 80px;
            height: 60px;
            border-radius: 8px;
            background: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop') center/cover;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .info-title {
            flex: 1;
        }

        .museum-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .museum-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .info-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 12px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            color: #555;
        }

        .detail-icon {
            margin-right: 4px;
            font-size: 14px;
        }

        /* 悬浮导航按钮 */
        .nav-buttons {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .nav-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 18px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            min-width: 80px;
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
        }

        .nav-btn:not(.active) {
            background: transparent;
            color: #666;
        }

        .nav-btn:not(.active):hover {
            background: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
        }

        /* 内容区域 */
        .content {
            padding: 15px;
            padding-bottom: 160px;
            min-height: calc(100vh - 200px);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* 智能问答样式 */
        .qa-section {
            animation: fadeIn 0.5s ease;
        }

        /* 推荐攻略横向展示 */
        .recommendations {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .rec-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .rec-scroll::-webkit-scrollbar {
            display: none;
        }

        .rec-card {
            flex-shrink: 0;
            width: 120px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .rec-card:hover {
            transform: translateY(-2px);
        }

        .rec-image {
            width: 100%;
            height: 80px;
            background-size: cover;
            background-position: center;
        }

        .rec-content {
            padding: 8px;
        }

        .rec-title {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            line-height: 1.3;
        }

        /* 常见问题样式 */
        .faq-section {
            margin-bottom: 20px;
        }

        .faq-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .faq-item:hover {
            border-color: #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
        }

        .faq-question {
            font-weight: bold;
            color: #333;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .faq-arrow {
            font-size: 12px;
            color: #666;
            transition: transform 0.3s ease;
        }

        .faq-item.expanded .faq-arrow {
            transform: rotate(180deg);
        }

        /* 智能导览样式 */
        .guide-section {
            animation: fadeIn 0.5s ease;
        }

        .floor-map {
            width: 100%;
            height: 250px;
            background: #f0f8ff;
            border-radius: 15px;
            margin-bottom: 20px;
            position: relative;
            border: 2px solid #4CAF50;
            overflow: hidden;
        }

        .floor-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }

        .map-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }

        .exhibition-halls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .hall-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .hall-card:hover {
            transform: translateY(-3px);
            border-color: #4CAF50;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
        }

        .hall-card.selected {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .hall-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .hall-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .hall-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 知识科普样式 */
        .knowledge-section {
            animation: fadeIn 0.5s ease;
        }

        .knowledge-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .knowledge-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .knowledge-card:hover {
            transform: translateY(-2px);
        }

        .knowledge-image {
            width: 100%;
            height: 100px;
            background-size: cover;
            background-position: center;
        }

        .knowledge-content {
            padding: 12px;
        }

        .knowledge-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
        }

        .knowledge-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .image-preview {
            width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin-bottom: 15px;
            display: none;
        }

        .voice-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .voice-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .stop-btn {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        /* 输入框样式 */
        .input-section {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-field {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            border-color: #4CAF50;
        }

        .send-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .hidden {
            display: none !important;
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .nav-btn {
                font-size: 12px;
                padding: 10px 6px;
            }
            
            .header-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 紧凑的基本信息 -->
        <div class="basic-info">
            <div class="info-header">
                <div class="museum-thumb"></div>
                <div class="info-title">
                    <div class="museum-name">科技馆伴游助手</div>
                    <div class="museum-desc">探索科技奥秘，开启智慧之旅</div>
                </div>
            </div>
            <div class="info-details">
                <div class="detail-item">
                    <span class="detail-icon">📍</span>
                    <span>科技大道123号</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">🕒</span>
                    <span>9:00-17:00</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">💰</span>
                    <span>成人30元</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">📞</span>
                    <span>400-123-4567</span>
                </div>
            </div>
        </div>

        <!-- 悬浮导航按钮 -->
        <div class="nav-buttons">
            <button class="nav-btn active" data-section="qa">
                🤖 问答
            </button>
            <button class="nav-btn" data-section="guide">
                🗺️ 导览
            </button>
            <button class="nav-btn" data-section="knowledge">
                📚 科普
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 智能问答 -->
            <div class="content-section active" id="qa-section">
                <div class="qa-section">
                    <!-- 推荐攻略 -->
                    <div class="recommendations">
                        <div class="section-title">🔥 推荐攻略</div>
                        <div class="rec-scroll">
                            <div class="rec-card" data-route="family">
                                <div class="rec-image" style="background-image: url('https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=300&h=200&fit=crop')"></div>
                                <div class="rec-content">
                                    <div class="rec-title">亲子游路线</div>
                                </div>
                            </div>
                            <div class="rec-card" data-route="science">
                                <div class="rec-image" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop')"></div>
                                <div class="rec-content">
                                    <div class="rec-title">科学探索</div>
                                </div>
                            </div>
                            <div class="rec-card" data-route="tech">
                                <div class="rec-image" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=200&fit=crop')"></div>
                                <div class="rec-content">
                                    <div class="rec-title">科技体验</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 常见问题 -->
                    <div class="faq-section">
                        <div class="section-title">❓ 常见问题</div>
                        <div class="faq-item">
                            <div class="faq-question">
                                🚗 停车信息
                                <span class="faq-arrow">▼</span>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                🍽️ 餐饮服务
                                <span class="faq-arrow">▼</span>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                🎫 如何预约特展
                                <span class="faq-arrow">▼</span>
                            </div>
                        </div>
                        <div class="faq-item">
                            <div class="faq-question">
                                💰 门票优惠政策
                                <span class="faq-arrow">▼</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 智能导览 -->
            <div class="content-section" id="guide-section">
                <div class="guide-section">
                    <div class="floor-map" id="floorMap">
                        <div class="floor-indicator" id="floorIndicator">F1 - 好奇探索层</div>
                        <div class="map-content">
                            <div style="font-size: 48px; margin-bottom: 10px;">🗺️</div>
                            <div>点击下方展厅查看位置</div>
                        </div>
                    </div>

                    <h3 style="margin-bottom: 15px; color: #4CAF50;">🏛️ 展厅导览</h3>
                    <div class="exhibition-halls">
                        <div class="hall-card" data-floor="F1" data-hall="好奇">
                            <div class="hall-icon">🔬</div>
                            <div class="hall-name">好奇展区</div>
                            <div class="hall-desc">能量·自然·安全·智慧</div>
                        </div>
                        <div class="hall-card" data-floor="F1" data-hall="自然">
                            <div class="hall-icon">🌿</div>
                            <div class="hall-name">自然展区</div>
                            <div class="hall-desc">缤纷自然·和谐共生</div>
                        </div>
                        <div class="hall-card" data-floor="F1" data-hall="我们">
                            <div class="hall-icon">🧠</div>
                            <div class="hall-name">我们展区</div>
                            <div class="hall-desc">身体与意识·神奇大脑</div>
                        </div>
                        <div class="hall-card" data-floor="F2" data-hall="互动">
                            <div class="hall-icon">🎮</div>
                            <div class="hall-name">F2互动区</div>
                            <div class="hall-desc">科技体验·互动游戏</div>
                        </div>
                        <div class="hall-card" data-floor="F3" data-hall="影院">
                            <div class="hall-icon">🎬</div>
                            <div class="hall-name">特色影院</div>
                            <div class="hall-desc">4D·飞翔·全息剧场</div>
                        </div>
                        <div class="hall-card" data-floor="F4" data-hall="教育">
                            <div class="hall-icon">📚</div>
                            <div class="hall-name">F4教育区</div>
                            <div class="hall-desc">科学课程·知识科普</div>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 20px;">
                        <h4 style="color: #4CAF50; margin-bottom: 10px;">🎯 预约服务</h4>
                        <p style="font-size: 14px; color: #666; line-height: 1.5;">
                            • 特展预约：全息动物园、科技探险队、AR交互体验<br>
                            • 展项预约：蒸汽小火车、高铁模拟驾驶、高空自行车、地震体验<br>
                            • 课程预约：科学初体验课程
                        </p>
                    </div>
                </div>
            </div>

            <!-- 知识科普 -->
            <div class="content-section" id="knowledge-section">
                <div class="knowledge-section">
                    <div class="section-title">📚 科学知识</div>
                    <div class="knowledge-grid">
                        <div class="knowledge-card" data-topic="physics">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">物理原理</div>
                                <div class="knowledge-desc">探索力学、光学、电磁学等基础物理知识</div>
                            </div>
                        </div>
                        <div class="knowledge-card" data-topic="biology">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">生物科学</div>
                                <div class="knowledge-desc">了解生命的奥秘和生物多样性</div>
                            </div>
                        </div>
                        <div class="knowledge-card" data-topic="chemistry">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">化学反应</div>
                                <div class="knowledge-desc">观察神奇的化学变化过程</div>
                            </div>
                        </div>
                        <div class="knowledge-card" data-topic="technology">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">前沿科技</div>
                                <div class="knowledge-desc">体验人工智能和未来科技</div>
                            </div>
                        </div>
                        <div class="knowledge-card" data-topic="space">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">宇宙探索</div>
                                <div class="knowledge-desc">探索浩瀚宇宙的无穷奥秘</div>
                            </div>
                        </div>
                        <div class="knowledge-card" data-topic="environment">
                            <div class="knowledge-image" style="background-image: url('https://images.unsplash.com/photo-1569163139394-de4e4f43e4e5?w=300&h=200&fit=crop')"></div>
                            <div class="knowledge-content">
                                <div class="knowledge-title">环保科学</div>
                                <div class="knowledge-desc">学习环境保护和可持续发展</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入框 -->
        <div class="input-section">
            <div class="input-container">
                <input type="text" class="input-field" id="userInput" placeholder="请输入您的问题...">
                <button class="send-btn" id="sendBtn">➤</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'qa';
        let currentFloor = 'F1';

        // 展厅数据
        const hallsData = {
            '好奇': {
                floor: 'F1',
                name: '好奇展区',
                description: '这里是好奇展区，包含能量展区、自然展区、安全展区和智慧展区。通过互动体验，激发您对科学的好奇心。',
                features: ['能量转换实验', '自然现象模拟', '安全知识体验', '人工智能展示']
            },
            '自然': {
                floor: 'F1',
                name: '自然展区',
                description: '探索缤纷自然世界，了解生物的适应奇技，感受人与自然的和谐共生关系。',
                features: ['生物多样性展示', '生态系统模型', '环保互动体验', '自然声音体验']
            },
            '我们': {
                floor: 'F1',
                name: '我们展区',
                description: '深入了解人体奥秘，探索身体与意识的关系，揭开神奇大脑的秘密。',
                features: ['人体结构模型', '大脑功能展示', '感官体验区', '健康知识普及']
            },
            '互动': {
                floor: 'F2',
                name: 'F2互动区',
                description: 'F2层互动体验区，提供各种科技互动游戏和体验项目。',
                features: ['VR虚拟现实', '机器人互动', '编程体验', '科技游戏']
            },
            '影院': {
                floor: 'F3',
                name: '特色影院',
                description: 'F3层特色影院区，包含4D影院、飞翔影院和全息剧场，带来震撼视听体验。',
                features: ['4D沉浸体验', '飞翔模拟器', '全息投影秀', '环幕电影']
            },
            '教育': {
                floor: 'F4',
                name: 'F4教育区',
                description: 'F4层教育区域，提供科学课程和知识科普活动。',
                features: ['科学实验课', '知识讲座', '手工制作', '科普阅读']
            }
        };



        // DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // 初始化应用
        function initializeApp() {
            setupNavigation();
            setupRecommendations();
            setupFAQ();
            setupGuide();
            setupKnowledge();
            setupInput();
            adjustContentPadding();
        }

        // 设置导航功能
        function setupNavigation() {
            const navBtns = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.content-section');

            navBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetSection = this.dataset.section;

                    // 更新按钮状态
                    navBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 更新内容区域
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(targetSection + '-section').classList.add('active');

                    currentSection = targetSection;
                });
            });
        }

        // 设置推荐攻略功能
        function setupRecommendations() {
            const recCards = document.querySelectorAll('.rec-card');

            recCards.forEach(card => {
                card.addEventListener('click', function() {
                    const route = this.dataset.route;
                    openRouteDetail(route);
                });
            });
        }

        // 打开路线详情页面
        function openRouteDetail(route) {
            const routeData = {
                'family': {
                    title: '亲子游路线',
                    content: '适合家庭的科技馆游览路线，包含互动性强的展区和适合儿童的科普内容。'
                },
                'science': {
                    title: '科学探索路线',
                    content: '深度科学体验路线，重点参观实验室和科学原理展示区域。'
                },
                'tech': {
                    title: '科技体验路线',
                    content: '前沿科技体验路线，包含VR、AI、机器人等高科技展项。'
                }
            };

            const data = routeData[route];
            if (data) {
                // 创建新页面内容
                const newWindow = window.open('', '_blank');
                newWindow.document.write(`
                    <!DOCTYPE html>
                    <html lang="zh-CN">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>${data.title} - 科技馆伴游助手</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
                            .header { text-align: center; margin-bottom: 30px; }
                            .title { font-size: 24px; color: #4CAF50; margin-bottom: 10px; }
                            .content { line-height: 1.6; color: #333; }
                            .back-btn { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1 class="title">${data.title}</h1>
                            <button class="back-btn" onclick="window.close()">返回</button>
                        </div>
                        <div class="content">
                            <p>${data.content}</p>
                            <p>详细路线规划正在完善中，敬请期待...</p>
                        </div>
                    </body>
                    </html>
                `);
            }
        }

        // 设置FAQ功能
        function setupFAQ() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                item.addEventListener('click', function() {
                    const question = this.querySelector('.faq-question').textContent.trim();
                    handleFAQClick(question);
                });
            });
        }

        // 处理FAQ点击
        function handleFAQClick(question) {
            let response = '';

            if (question.includes('停车')) {
                response = '🚗 停车信息：\n\n科技馆提供免费停车场，共500个车位。停车场位于科技馆东侧，24小时开放。建议您提前到达，特别是周末和节假日车位会比较紧张。如果停车场满了，附近还有收费停车场可供选择。';
            } else if (question.includes('餐饮')) {
                response = '🍽️ 餐饮服务：\n\n馆内设有多个餐饮区域：\n• F1层便民餐厅：中式简餐、盖饭类\n• F2层咖啡厅：轻食、饮品、甜点\n• F3层快餐区：汉堡、炸鸡、饮料\n\n价格亲民，人均消费20-40元，营业时间与展馆同步。';
            } else if (question.includes('预约')) {
                response = '🎫 预约方式：\n\n1. 官方微信小程序"科技馆助手"\n2. 现场扫码预约（入口处二维码）\n3. 官网在线预约\n4. 电话预约：400-123-4567\n\n热门展项如4D影院、VR体验建议提前1-2天预约。';
            } else if (question.includes('门票') || question.includes('优惠')) {
                response = '💰 门票优惠政策：\n\n• 成人票：30元\n• 学生票：15元（凭学生证）\n• 儿童票：1.2米以下免费\n• 老人票：65岁以上半价\n• 团体票：20人以上8折\n• 年卡：200元（一年内无限次）';
            } else {
                response = '感谢您的咨询！如需了解更多信息，请在下方输入框中详细描述您的问题，我会为您提供准确的解答。';
            }

            showAIResponse(response);
        }

        // 设置导览功能
        function setupGuide() {
            const hallCards = document.querySelectorAll('.hall-card');

            hallCards.forEach(card => {
                card.addEventListener('click', function() {
                    const floor = this.dataset.floor;
                    const hall = this.dataset.hall;

                    // 更新选中状态
                    hallCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');

                    // 更新楼层显示
                    updateFloorMap(floor, hall);

                    // 显示展厅信息
                    showHallInfo(hall);
                });
            });
        }

        // 更新楼层地图
        function updateFloorMap(floor, hall) {
            const floorIndicator = document.getElementById('floorIndicator');
            const mapContent = document.querySelector('.map-content');

            floorIndicator.textContent = `${floor} - ${hallsData[hall].name}`;

            // 更新地图内容
            mapContent.innerHTML = `
                <div style="font-size: 48px; margin-bottom: 10px;">📍</div>
                <div style="font-weight: bold; color: #4CAF50;">${hallsData[hall].name}</div>
                <div style="font-size: 14px; margin-top: 5px;">${floor}层 - 已定位</div>
            `;

            currentFloor = floor;
        }

        // 显示展厅信息
        function showHallInfo(hall) {
            const hallData = hallsData[hall];
            if (!hallData) return;

            // 更新输入框提示
            const inputField = document.getElementById('userInput');
            inputField.placeholder = `了解${hallData.name}的更多信息...`;

            // 模拟AI回复
            setTimeout(() => {
                showAIResponse(`${hallData.description}\n\n主要特色：\n${hallData.features.map(f => `• ${f}`).join('\n')}`);
            }, 500);
        }

        // 设置知识科普功能
        function setupKnowledge() {
            const knowledgeCards = document.querySelectorAll('.knowledge-card');

            knowledgeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const topic = this.dataset.topic;
                    showKnowledgeDetail(topic);
                });
            });
        }

        // 显示知识详情
        function showKnowledgeDetail(topic) {
            const knowledgeData = {
                'physics': {
                    title: '物理原理',
                    content: '物理学是研究物质运动规律和物质基本结构的科学。在科技馆中，您可以通过各种互动实验了解力学、光学、电磁学等基础物理知识。比如通过摆锤实验理解能量守恒定律，通过光学实验了解光的折射和反射原理。'
                },
                'biology': {
                    title: '生物科学',
                    content: '生物科学探索生命的奥秘和生物多样性。展馆内的生物展区展示了从微观细胞到宏观生态系统的各个层面。您可以通过显微镜观察细胞结构，了解DNA的双螺旋结构，探索生物进化的历程。'
                },
                'chemistry': {
                    title: '化学反应',
                    content: '化学是研究物质组成、结构、性质和变化规律的科学。在化学实验区，您可以观察到各种神奇的化学反应，如酸碱中和、氧化还原反应等。安全的实验环境让您亲身体验化学的魅力。'
                },
                'technology': {
                    title: '前沿科技',
                    content: '前沿科技展区展示了人工智能、机器学习、量子计算等最新科技成果。您可以与智能机器人对话，体验VR虚拟现实技术，了解5G通信原理，感受科技改变生活的力量。'
                },
                'space': {
                    title: '宇宙探索',
                    content: '宇宙探索展区带您遨游浩瀚星空。通过天文望远镜观察星体，了解太阳系的构成，探索黑洞的奥秘。互动式星空投影让您仿佛置身于宇宙之中，感受宇宙的无穷魅力。'
                },
                'environment': {
                    title: '环保科学',
                    content: '环保科学展区关注地球环境和可持续发展。您可以了解气候变化的原因和影响，学习垃圾分类和资源回收的重要性，探索清洁能源技术，培养环保意识。'
                }
            };

            const data = knowledgeData[topic];
            if (data) {
                showAIResponse(`📚 ${data.title}\n\n${data.content}\n\n💡 建议您到相关展区进行实地体验，会有更深刻的理解！`);
            }
        }



        // 设置输入功能
        function setupInput() {
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');

            // 发送按钮点击
            sendBtn.addEventListener('click', function() {
                handleUserInput();
            });

            // 回车键发送
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleUserInput();
                }
            });
        }

        // 处理用户输入
        function handleUserInput() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();

            if (!message) return;

            // 清空输入框
            userInput.value = '';

            // 处理不同类型的问题
            processUserMessage(message);
        }

        // 处理用户消息
        function processUserMessage(message) {
            const lowerMessage = message.toLowerCase();
            let response = '';

            // 根据当前页面和消息内容生成回复
            if (currentSection === 'qa') {
                response = handleQAMessage(lowerMessage);
            } else if (currentSection === 'guide') {
                response = handleGuideMessage(lowerMessage);
            } else if (currentSection === 'knowledge') {
                response = handleKnowledgeMessage(lowerMessage);
            }

            // 显示AI回复
            showAIResponse(response);
        }

        // 处理问答消息
        function handleQAMessage(message) {
            if (message.includes('停车') || message.includes('车位')) {
                return '科技馆提供免费停车场，共500个车位。建议您提前到达，特别是周末车位会比较紧张。停车场位于科技馆东侧，步行到入口约3分钟。';
            } else if (message.includes('餐饮') || message.includes('吃饭') || message.includes('食物')) {
                return '馆内设有多个餐饮区域：F1层便民餐厅提供中式简餐，F2层咖啡厅提供轻食和饮品，F3层设有快餐区。价格亲民，营业时间与展馆同步。';
            } else if (message.includes('预约') || message.includes('票')) {
                return '您可以通过以下方式预约：1. 官方微信小程序 2. 现场扫码预约 3. 官网在线预约。热门展项如4D影院、VR体验建议提前1-2天预约。';
            } else if (message.includes('时间') || message.includes('开放')) {
                return '科技馆开放时间：周二至周日 9:00-17:00，周一闭馆维护。最晚入场时间为16:00，建议您合理安排参观时间。';
            } else if (message.includes('价格') || message.includes('门票') || message.includes('多少钱')) {
                return '门票价格：成人票30元，学生票15元（凭学生证），儿童1.2米以下免费。团体票（20人以上）可享受8折优惠。';
            } else {
                return '您好！我是科技馆智能助手。您可以询问关于开放时间、票价、停车、餐饮、预约等任何问题。我会为您提供详细的解答。';
            }
        }

        // 处理导览消息
        function handleGuideMessage(message) {
            if (message.includes('好奇') || message.includes('能量') || message.includes('安全')) {
                return '好奇展区位于F1层，包含能量展区、自然展区、安全展区和智慧展区。这里有丰富的互动体验项目，特别适合激发对科学的好奇心。建议游览时间1-1.5小时。';
            } else if (message.includes('自然') || message.includes('生物') || message.includes('环保')) {
                return '自然展区展示了缤纷的自然世界，您可以了解生物的适应奇技，感受人与自然的和谐共生。推荐体验生态系统模型和环保互动项目。';
            } else if (message.includes('大脑') || message.includes('身体') || message.includes('人体')) {
                return '"我们"展区深入探索人体奥秘，特别是神奇的大脑功能。这里有详细的人体结构模型和感官体验区，非常适合了解自己的身体。';
            } else if (message.includes('影院') || message.includes('4d') || message.includes('电影')) {
                return 'F3层特色影院包含4D影院、飞翔影院和全息剧场。4D影院每场20分钟，建议提前预约。飞翔影院可以体验翱翔天空的感觉。';
            } else if (message.includes('路线') || message.includes('怎么走') || message.includes('推荐')) {
                return '推荐游览路线：F1好奇展区(1.5h) → F1自然展区(1h) → F3特色影院(0.5h) → F2互动体验(1h) → F4教育区(1h)。总计约5小时，建议安排一整天。';
            } else {
                return `当前您在查看${currentFloor}层导览。您可以询问具体展厅信息、推荐路线、游览时间等。点击展厅卡片可以查看详细位置和介绍。`;
            }
        }

        // 处理知识科普消息
        function handleKnowledgeMessage(message) {
            if (message.includes('物理') || message.includes('力学') || message.includes('光学')) {
                return '物理学是自然科学的基础。在科技馆的物理展区，您可以通过互动实验了解力学、光学、电磁学等原理。推荐体验：摆锤实验、光的折射实验、静电演示等。';
            } else if (message.includes('生物') || message.includes('细胞') || message.includes('dna')) {
                return '生物科学探索生命的奥秘。展区内有显微镜观察区、DNA模型展示、生态系统模拟等。您可以近距离观察细胞结构，了解生命的基本组成。';
            } else if (message.includes('化学') || message.includes('反应') || message.includes('实验')) {
                return '化学展区提供安全的实验环境，您可以观察各种化学反应。包括酸碱中和、氧化还原、结晶过程等。所有实验都有专业指导，确保安全。';
            } else if (message.includes('科技') || message.includes('ai') || message.includes('人工智能')) {
                return '前沿科技展区展示最新科技成果。您可以体验VR虚拟现实、与AI机器人对话、了解5G通信原理。这里是感受未来科技的最佳场所。';
            } else if (message.includes('宇宙') || message.includes('太空') || message.includes('星空')) {
                return '宇宙探索展区带您遨游星空。通过天文望远镜观察星体，了解太阳系构成，探索黑洞奥秘。互动式星空投影让您身临其境。';
            } else if (message.includes('环保') || message.includes('环境') || message.includes('气候')) {
                return '环保科学展区关注地球环境。您可以了解气候变化、学习垃圾分类、探索清洁能源技术。培养环保意识，从科技馆开始。';
            } else {
                return '知识科普区域涵盖物理、生物、化学、科技、宇宙、环保六大主题。点击相应卡片可以了解详细内容，或者告诉我您感兴趣的科学领域。';
            }
        }

        // 显示AI回复
        function showAIResponse(response) {
            // 创建临时提示框显示回复
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                max-width: 300px;
                z-index: 1000;
                font-size: 14px;
                line-height: 1.5;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                white-space: pre-line;
            `;
            toast.textContent = response;

            document.body.appendChild(toast);

            // 4秒后自动消失
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 4000);

            // 点击消失
            toast.addEventListener('click', () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
        }

        // 调整内容底部间距
        function adjustContentPadding() {
            const inputSection = document.querySelector('.input-section');
            const content = document.querySelector('.content');
            if (inputSection && content) {
                content.style.paddingBottom = inputSection.offsetHeight + 20 + 'px';
            }
        }

        // 防止页面滚动时输入框被遮挡
        window.addEventListener('resize', adjustContentPadding);
        window.addEventListener('load', adjustContentPadding);

        // 添加一些额外的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有按钮添加点击反馈
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 为卡片添加点击反馈
            const cards = document.querySelectorAll('.hall-card, .faq-item');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
