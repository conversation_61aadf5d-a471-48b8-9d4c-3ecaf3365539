<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技馆伴游助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 头部宣传图片 */
        .header-banner {
            width: 100%;
            height: 200px;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><rect fill="%23667eea" width="400" height="200"/><circle fill="%23ffffff" opacity="0.1" cx="100" cy="50" r="30"/><circle fill="%23ffffff" opacity="0.1" cx="300" cy="150" r="40"/><rect fill="%23ffffff" opacity="0.2" x="150" y="80" width="100" height="40" rx="20"/><text x="200" y="105" text-anchor="middle" fill="white" font-size="16" font-weight="bold">科技馆</text></svg>');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
        }

        .header-content {
            text-align: center;
            z-index: 2;
        }

        .header-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* 导航按钮 */
        .nav-buttons {
            display: flex;
            padding: 20px 15px;
            gap: 10px;
            background: #f8f9fa;
        }

        .nav-btn {
            flex: 1;
            padding: 12px 8px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .nav-btn:not(.active) {
            background: white;
            color: #666;
            border: 2px solid #e0e0e0;
        }

        .nav-btn:not(.active):hover {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        /* 内容区域 */
        .content {
            padding: 20px;
            min-height: calc(100vh - 320px);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* 智能问答样式 */
        .qa-section {
            animation: fadeIn 0.5s ease;
        }

        .museum-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .museum-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .info-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-label {
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .faq-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .faq-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .faq-question {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .faq-answer {
            color: #666;
            font-size: 14px;
            display: none;
        }

        .faq-item.expanded .faq-answer {
            display: block;
            margin-top: 10px;
        }

        /* 智能导览样式 */
        .guide-section {
            animation: fadeIn 0.5s ease;
        }

        .floor-map {
            width: 100%;
            height: 250px;
            background: #f0f8ff;
            border-radius: 15px;
            margin-bottom: 20px;
            position: relative;
            border: 2px solid #4CAF50;
            overflow: hidden;
        }

        .floor-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }

        .map-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }

        .exhibition-halls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .hall-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .hall-card:hover {
            transform: translateY(-3px);
            border-color: #4CAF50;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.2);
        }

        .hall-card.selected {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .hall-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .hall-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .hall-desc {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 看图识景样式 */
        .vision-section {
            animation: fadeIn 0.5s ease;
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #4CAF50;
            border-radius: 15px;
            padding: 40px 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8fff8;
        }

        .upload-area:hover {
            background: #f0fff0;
            border-color: #45a049;
        }

        .upload-icon {
            font-size: 48px;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            font-size: 16px;
        }

        .image-preview {
            width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin-bottom: 15px;
            display: none;
        }

        .voice-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .voice-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .stop-btn {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        /* 输入框样式 */
        .input-section {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            padding: 15px;
            border-top: 1px solid #e0e0e0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-field {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            border-color: #4CAF50;
        }

        .send-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .hidden {
            display: none !important;
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .nav-btn {
                font-size: 12px;
                padding: 10px 6px;
            }
            
            .header-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部宣传图片 -->
        <div class="header-banner">
            <div class="header-content">
                <h1 class="header-title">科技馆伴游助手</h1>
                <p class="header-subtitle">探索科技奥秘，开启智慧之旅</p>
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="nav-buttons">
            <button class="nav-btn active" data-section="qa">
                🤖 智能问答
            </button>
            <button class="nav-btn" data-section="guide">
                🗺️ 智能导览
            </button>
            <button class="nav-btn" data-section="vision">
                📷 看图识景
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 智能问答 -->
            <div class="content-section active" id="qa-section">
                <div class="qa-section">
                    <div class="museum-info">
                        <div class="museum-image">🏛️ 科技馆实景</div>
                        <div class="info-item">
                            <div class="info-label">📍 地址</div>
                            <div>科技大道123号，科技园区内</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">🕒 开放时间</div>
                            <div>周二至周日 9:00-17:00（周一闭馆）</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">💰 门票价格</div>
                            <div>成人票30元，学生票15元，儿童免费</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">📞 咨询电话</div>
                            <div>400-123-4567</div>
                        </div>
                    </div>

                    <h3 style="margin-bottom: 15px; color: #4CAF50;">🔥 推荐攻略</h3>
                    <div class="faq-item">
                        <div class="faq-question">🎯 一日游最佳路线</div>
                        <div class="faq-answer">建议路线：F1好奇展区 → F2特色展厅 → 4D影院 → F3互动体验 → F4科普教育。预计游览时间4-5小时。</div>
                    </div>

                    <h3 style="margin: 20px 0 15px; color: #4CAF50;">❓ 常见问题</h3>
                    <div class="faq-item">
                        <div class="faq-question">🚗 停车信息</div>
                        <div class="faq-answer">科技馆提供免费停车场，共500个车位。建议提前到达，周末车位较紧张。</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">🍽️ 餐饮服务</div>
                        <div class="faq-answer">馆内设有咖啡厅和快餐区，F1层有便民餐厅，提供简餐和饮品。</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">🎫 如何预约特展</div>
                        <div class="faq-answer">可通过官方微信小程序或现场扫码预约。热门展项建议提前1-2天预约。</div>
                    </div>
                </div>
            </div>

            <!-- 智能导览 -->
            <div class="content-section" id="guide-section">
                <div class="guide-section">
                    <div class="floor-map" id="floorMap">
                        <div class="floor-indicator" id="floorIndicator">F1 - 好奇探索层</div>
                        <div class="map-content">
                            <div style="font-size: 48px; margin-bottom: 10px;">🗺️</div>
                            <div>点击下方展厅查看位置</div>
                        </div>
                    </div>

                    <h3 style="margin-bottom: 15px; color: #4CAF50;">🏛️ 展厅导览</h3>
                    <div class="exhibition-halls">
                        <div class="hall-card" data-floor="F1" data-hall="好奇">
                            <div class="hall-icon">🔬</div>
                            <div class="hall-name">好奇展区</div>
                            <div class="hall-desc">能量·自然·安全·智慧</div>
                        </div>
                        <div class="hall-card" data-floor="F1" data-hall="自然">
                            <div class="hall-icon">🌿</div>
                            <div class="hall-name">自然展区</div>
                            <div class="hall-desc">缤纷自然·和谐共生</div>
                        </div>
                        <div class="hall-card" data-floor="F1" data-hall="我们">
                            <div class="hall-icon">🧠</div>
                            <div class="hall-name">我们展区</div>
                            <div class="hall-desc">身体与意识·神奇大脑</div>
                        </div>
                        <div class="hall-card" data-floor="F2" data-hall="互动">
                            <div class="hall-icon">🎮</div>
                            <div class="hall-name">F2互动区</div>
                            <div class="hall-desc">科技体验·互动游戏</div>
                        </div>
                        <div class="hall-card" data-floor="F3" data-hall="影院">
                            <div class="hall-icon">🎬</div>
                            <div class="hall-name">特色影院</div>
                            <div class="hall-desc">4D·飞翔·全息剧场</div>
                        </div>
                        <div class="hall-card" data-floor="F4" data-hall="教育">
                            <div class="hall-icon">📚</div>
                            <div class="hall-name">F4教育区</div>
                            <div class="hall-desc">科学课程·知识科普</div>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 20px;">
                        <h4 style="color: #4CAF50; margin-bottom: 10px;">🎯 预约服务</h4>
                        <p style="font-size: 14px; color: #666; line-height: 1.5;">
                            • 特展预约：全息动物园、科技探险队、AR交互体验<br>
                            • 展项预约：蒸汽小火车、高铁模拟驾驶、高空自行车、地震体验<br>
                            • 课程预约：科学初体验课程
                        </p>
                    </div>
                </div>
            </div>

            <!-- 看图识景 -->
            <div class="content-section" id="vision-section">
                <div class="vision-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">点击上传展厅图片<br>AI将为您语音介绍</div>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    <img id="imagePreview" class="image-preview" alt="预览图片">
                    
                    <div id="recognitionResult" style="display: none;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                            <h4 style="color: #4CAF50; margin-bottom: 10px;">🎯 识别结果</h4>
                            <p id="resultText" style="color: #333; line-height: 1.6;">
                                正在分析图片内容...
                            </p>
                        </div>
                        
                        <div class="voice-controls">
                            <button class="voice-btn play-btn" id="playBtn">🔊 播放介绍</button>
                            <button class="voice-btn stop-btn" id="stopBtn">⏹️ 停止播放</button>
                        </div>
                    </div>

                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin-top: 20px;">
                        <h4 style="color: #4CAF50; margin-bottom: 10px;">💡 使用提示</h4>
                        <p style="font-size: 14px; color: #666; line-height: 1.5;">
                            • 拍摄展品或展厅全景效果更佳<br>
                            • 确保图片清晰，光线充足<br>
                            • 支持JPG、PNG格式图片<br>
                            • AI将自动识别并提供详细介绍
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入框 -->
        <div class="input-section">
            <div class="input-container">
                <input type="text" class="input-field" id="userInput" placeholder="请输入您的问题...">
                <button class="send-btn" id="sendBtn">➤</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'qa';
        let currentFloor = 'F1';
        let isPlaying = false;
        let speechSynthesis = window.speechSynthesis;
        let currentUtterance = null;

        // 展厅数据
        const hallsData = {
            '好奇': {
                floor: 'F1',
                name: '好奇展区',
                description: '这里是好奇展区，包含能量展区、自然展区、安全展区和智慧展区。通过互动体验，激发您对科学的好奇心。',
                features: ['能量转换实验', '自然现象模拟', '安全知识体验', '人工智能展示']
            },
            '自然': {
                floor: 'F1',
                name: '自然展区',
                description: '探索缤纷自然世界，了解生物的适应奇技，感受人与自然的和谐共生关系。',
                features: ['生物多样性展示', '生态系统模型', '环保互动体验', '自然声音体验']
            },
            '我们': {
                floor: 'F1',
                name: '我们展区',
                description: '深入了解人体奥秘，探索身体与意识的关系，揭开神奇大脑的秘密。',
                features: ['人体结构模型', '大脑功能展示', '感官体验区', '健康知识普及']
            },
            '互动': {
                floor: 'F2',
                name: 'F2互动区',
                description: 'F2层互动体验区，提供各种科技互动游戏和体验项目。',
                features: ['VR虚拟现实', '机器人互动', '编程体验', '科技游戏']
            },
            '影院': {
                floor: 'F3',
                name: '特色影院',
                description: 'F3层特色影院区，包含4D影院、飞翔影院和全息剧场，带来震撼视听体验。',
                features: ['4D沉浸体验', '飞翔模拟器', '全息投影秀', '环幕电影']
            },
            '教育': {
                floor: 'F4',
                name: 'F4教育区',
                description: 'F4层教育区域，提供科学课程和知识科普活动。',
                features: ['科学实验课', '知识讲座', '手工制作', '科普阅读']
            }
        };

        // 图片识别结果数据
        const recognitionResults = {
            '能量展区': '这是能量展区，展示了各种能量转换原理。您可以通过互动装置体验机械能、电能、热能之间的转换过程。',
            '自然展区': '这是自然展区，模拟了各种自然环境和生态系统。您可以观察到不同生物的生存策略和适应机制。',
            '大脑展示': '这是神奇的大脑展示区，通过模型和互动设备，您可以了解大脑的结构和功能，探索意识的奥秘。',
            '4D影院': '这是4D影院，配备了先进的视听设备和体感装置，为您带来身临其境的观影体验。',
            '机器人': '这是机器人互动区，您可以与各种智能机器人对话交流，了解人工智能技术的发展。',
            '实验室': '这是科学实验室，提供各种科学实验体验，让您亲手操作，感受科学的魅力。'
        };

        // DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // 初始化应用
        function initializeApp() {
            setupNavigation();
            setupFAQ();
            setupGuide();
            setupVision();
            setupInput();
            adjustContentPadding();
        }

        // 设置导航功能
        function setupNavigation() {
            const navBtns = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.content-section');

            navBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetSection = this.dataset.section;

                    // 更新按钮状态
                    navBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 更新内容区域
                    sections.forEach(section => {
                        section.classList.remove('active');
                    });
                    document.getElementById(targetSection + '-section').classList.add('active');

                    currentSection = targetSection;
                });
            });
        }

        // 设置FAQ功能
        function setupFAQ() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.toggle('expanded');
                });
            });
        }

        // 设置导览功能
        function setupGuide() {
            const hallCards = document.querySelectorAll('.hall-card');

            hallCards.forEach(card => {
                card.addEventListener('click', function() {
                    const floor = this.dataset.floor;
                    const hall = this.dataset.hall;

                    // 更新选中状态
                    hallCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');

                    // 更新楼层显示
                    updateFloorMap(floor, hall);

                    // 显示展厅信息
                    showHallInfo(hall);
                });
            });
        }

        // 更新楼层地图
        function updateFloorMap(floor, hall) {
            const floorIndicator = document.getElementById('floorIndicator');
            const mapContent = document.querySelector('.map-content');

            floorIndicator.textContent = `${floor} - ${hallsData[hall].name}`;

            // 更新地图内容
            mapContent.innerHTML = `
                <div style="font-size: 48px; margin-bottom: 10px;">📍</div>
                <div style="font-weight: bold; color: #4CAF50;">${hallsData[hall].name}</div>
                <div style="font-size: 14px; margin-top: 5px;">${floor}层 - 已定位</div>
            `;

            currentFloor = floor;
        }

        // 显示展厅信息
        function showHallInfo(hall) {
            const hallData = hallsData[hall];
            if (!hallData) return;

            // 更新输入框提示
            const inputField = document.getElementById('userInput');
            inputField.placeholder = `了解${hallData.name}的更多信息...`;

            // 模拟AI回复
            setTimeout(() => {
                showAIResponse(`${hallData.description}\n\n主要特色：\n${hallData.features.map(f => `• ${f}`).join('\n')}`);
            }, 500);
        }

        // 设置看图识景功能
        function setupVision() {
            const uploadArea = document.getElementById('uploadArea');
            const imageInput = document.getElementById('imageInput');
            const imagePreview = document.getElementById('imagePreview');
            const recognitionResult = document.getElementById('recognitionResult');
            const resultText = document.getElementById('resultText');
            const playBtn = document.getElementById('playBtn');
            const stopBtn = document.getElementById('stopBtn');

            // 点击上传区域
            uploadArea.addEventListener('click', function() {
                imageInput.click();
            });

            // 文件选择
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.style.display = 'block';

                        // 模拟图片识别
                        recognizeImage();
                    };
                    reader.readAsDataURL(file);
                }
            });

            // 播放按钮
            playBtn.addEventListener('click', function() {
                const text = resultText.textContent;
                playVoice(text);
            });

            // 停止按钮
            stopBtn.addEventListener('click', function() {
                stopVoice();
            });
        }

        // 模拟图片识别
        function recognizeImage() {
            const recognitionResult = document.getElementById('recognitionResult');
            const resultText = document.getElementById('resultText');

            recognitionResult.style.display = 'block';
            resultText.textContent = '正在分析图片内容...';

            // 模拟识别过程
            setTimeout(() => {
                const results = Object.keys(recognitionResults);
                const randomResult = results[Math.floor(Math.random() * results.length)];
                const description = recognitionResults[randomResult];

                resultText.textContent = `识别到：${randomResult}\n\n${description}`;
            }, 2000);
        }

        // 语音播放
        function playVoice(text) {
            if (isPlaying) {
                stopVoice();
            }

            currentUtterance = new SpeechSynthesisUtterance(text);
            currentUtterance.lang = 'zh-CN';
            currentUtterance.rate = 0.8;
            currentUtterance.pitch = 1;

            currentUtterance.onstart = function() {
                isPlaying = true;
                document.getElementById('playBtn').textContent = '🔊 播放中...';
            };

            currentUtterance.onend = function() {
                isPlaying = false;
                document.getElementById('playBtn').textContent = '🔊 播放介绍';
            };

            speechSynthesis.speak(currentUtterance);
        }

        // 停止语音
        function stopVoice() {
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
            }
            isPlaying = false;
            document.getElementById('playBtn').textContent = '🔊 播放介绍';
        }

        // 设置输入功能
        function setupInput() {
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');

            // 发送按钮点击
            sendBtn.addEventListener('click', function() {
                handleUserInput();
            });

            // 回车键发送
            userInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleUserInput();
                }
            });
        }

        // 处理用户输入
        function handleUserInput() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();

            if (!message) return;

            // 清空输入框
            userInput.value = '';

            // 处理不同类型的问题
            processUserMessage(message);
        }

        // 处理用户消息
        function processUserMessage(message) {
            const lowerMessage = message.toLowerCase();
            let response = '';

            // 根据当前页面和消息内容生成回复
            if (currentSection === 'qa') {
                response = handleQAMessage(lowerMessage);
            } else if (currentSection === 'guide') {
                response = handleGuideMessage(lowerMessage);
            } else if (currentSection === 'vision') {
                response = handleVisionMessage(lowerMessage);
            }

            // 显示AI回复
            showAIResponse(response);
        }

        // 处理问答消息
        function handleQAMessage(message) {
            if (message.includes('停车') || message.includes('车位')) {
                return '科技馆提供免费停车场，共500个车位。建议您提前到达，特别是周末车位会比较紧张。停车场位于科技馆东侧，步行到入口约3分钟。';
            } else if (message.includes('餐饮') || message.includes('吃饭') || message.includes('食物')) {
                return '馆内设有多个餐饮区域：F1层便民餐厅提供中式简餐，F2层咖啡厅提供轻食和饮品，F3层设有快餐区。价格亲民，营业时间与展馆同步。';
            } else if (message.includes('预约') || message.includes('票')) {
                return '您可以通过以下方式预约：1. 官方微信小程序 2. 现场扫码预约 3. 官网在线预约。热门展项如4D影院、VR体验建议提前1-2天预约。';
            } else if (message.includes('时间') || message.includes('开放')) {
                return '科技馆开放时间：周二至周日 9:00-17:00，周一闭馆维护。最晚入场时间为16:00，建议您合理安排参观时间。';
            } else if (message.includes('价格') || message.includes('门票') || message.includes('多少钱')) {
                return '门票价格：成人票30元，学生票15元（凭学生证），儿童1.2米以下免费。团体票（20人以上）可享受8折优惠。';
            } else {
                return '您好！我是科技馆智能助手。您可以询问关于开放时间、票价、停车、餐饮、预约等任何问题。我会为您提供详细的解答。';
            }
        }

        // 处理导览消息
        function handleGuideMessage(message) {
            if (message.includes('好奇') || message.includes('能量') || message.includes('安全')) {
                return '好奇展区位于F1层，包含能量展区、自然展区、安全展区和智慧展区。这里有丰富的互动体验项目，特别适合激发对科学的好奇心。建议游览时间1-1.5小时。';
            } else if (message.includes('自然') || message.includes('生物') || message.includes('环保')) {
                return '自然展区展示了缤纷的自然世界，您可以了解生物的适应奇技，感受人与自然的和谐共生。推荐体验生态系统模型和环保互动项目。';
            } else if (message.includes('大脑') || message.includes('身体') || message.includes('人体')) {
                return '"我们"展区深入探索人体奥秘，特别是神奇的大脑功能。这里有详细的人体结构模型和感官体验区，非常适合了解自己的身体。';
            } else if (message.includes('影院') || message.includes('4d') || message.includes('电影')) {
                return 'F3层特色影院包含4D影院、飞翔影院和全息剧场。4D影院每场20分钟，建议提前预约。飞翔影院可以体验翱翔天空的感觉。';
            } else if (message.includes('路线') || message.includes('怎么走') || message.includes('推荐')) {
                return '推荐游览路线：F1好奇展区(1.5h) → F1自然展区(1h) → F3特色影院(0.5h) → F2互动体验(1h) → F4教育区(1h)。总计约5小时，建议安排一整天。';
            } else {
                return `当前您在查看${currentFloor}层导览。您可以询问具体展厅信息、推荐路线、游览时间等。点击展厅卡片可以查看详细位置和介绍。`;
            }
        }

        // 处理看图识景消息
        function handleVisionMessage(message) {
            if (message.includes('怎么用') || message.includes('如何') || message.includes('使用')) {
                return '使用方法很简单：1. 点击上传区域选择图片 2. 等待AI识别分析 3. 查看识别结果 4. 点击播放按钮听语音介绍。支持JPG、PNG格式，建议拍摄清晰的展品或展厅照片。';
            } else if (message.includes('识别') || message.includes('准确') || message.includes('效果')) {
                return 'AI识别系统基于深度学习技术，可以识别科技馆内的各种展品、展厅和设施。识别准确率达到95%以上，并提供详细的语音介绍和背景知识。';
            } else if (message.includes('语音') || message.includes('播放') || message.includes('声音')) {
                return '语音介绍功能支持中文播报，语速适中，内容详细。您可以随时暂停或重新播放。建议在安静环境下使用，以获得最佳体验效果。';
            } else {
                return '看图识景功能可以识别展厅内的展品并提供语音介绍。请上传清晰的图片，我会为您详细介绍相关内容。您也可以询问使用方法或功能特点。';
            }
        }

        // 显示AI回复
        function showAIResponse(response) {
            // 创建临时提示框显示回复
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                max-width: 300px;
                z-index: 1000;
                font-size: 14px;
                line-height: 1.5;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                white-space: pre-line;
            `;
            toast.textContent = response;

            document.body.appendChild(toast);

            // 4秒后自动消失
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 4000);

            // 点击消失
            toast.addEventListener('click', () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
        }

        // 调整内容底部间距
        function adjustContentPadding() {
            const inputSection = document.querySelector('.input-section');
            const content = document.querySelector('.content');
            if (inputSection && content) {
                content.style.paddingBottom = inputSection.offsetHeight + 20 + 'px';
            }
        }

        // 防止页面滚动时输入框被遮挡
        window.addEventListener('resize', adjustContentPadding);
        window.addEventListener('load', adjustContentPadding);

        // 添加一些额外的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有按钮添加点击反馈
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 为卡片添加点击反馈
            const cards = document.querySelectorAll('.hall-card, .faq-item');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
